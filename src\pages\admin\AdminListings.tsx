import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Database } from "@/integrations/supabase/types";

// Define types for property and car listings
type PropertyListing = {
  id: number; // Changed from string to number to match database BIGINT
  title: string;
  description: string;
  location: string;
  price: number;
  beds: number;
  baths: number;
  images: string[];
  owner_id: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  owner_first_name?: string;
  owner_last_name?: string;
};

type CarListing = {
  id: number; // Changed from string to number to match database BIGINT
  title: string;
  description: string;
  make: string;
  model: string;
  year: number;
  seats: number;
  transmission: string;
  fuel_type: string;
  price_day: number;
  price_week: number;
  price_month: number;
  images: string[];
  features: string[];
  car_type:
    | "sedan"
    | "suv"
    | "luxury"
    | "compact"
    | "convertible"
    | "van"
    | "truck";
  owner_id: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  owner_first_name?: string;
  owner_last_name?: string;
};

type HotelListing = {
  id: number; // Changed from string to number to match database BIGINT
  title: string;
  description: string;
  location: string;
  check_in_time: string;
  check_out_time: string;
  images: string[];
  amenities: string[];
  owner_id: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  owner_first_name?: string;
  owner_last_name?: string;
};

type Listing = PropertyListing | CarListing | HotelListing;

const AdminListings = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [properties, setProperties] = useState<PropertyListing[]>([]);
  const [hotels, setHotels] = useState<HotelListing[]>([]);
  const [cars, setCars] = useState<CarListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedListing, setSelectedListing] = useState<Listing | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    "all" | "pending" | "approved" | "rejected"
  >("all");

  useEffect(() => {
    fetchListings();
  }, [activeTab]);

  const fetchListings = async () => {
    setLoading(true);
    try {
      console.log("Fetching listings for tab:", activeTab);

      // Fetch properties
      let propertyQuery = supabase.from("properties").select("*");

      if (activeTab === "pending") {
        propertyQuery = propertyQuery.eq("status", "pending");
      } else if (activeTab === "approved") {
        propertyQuery = propertyQuery.eq("status", "approved");
      }

      const { data: propertiesData, error: propertiesError } =
        await propertyQuery;

      if (propertiesError) {
        console.error("Properties fetch error:", propertiesError);
        throw propertiesError;
      }

      console.log("Properties fetched:", propertiesData?.length || 0);

      // Fetch owner information for properties
      const ownerIds =
        propertiesData?.map((property) => property.owner_id) || [];
      const { data: ownersData, error: ownersError } = await supabase
        .from("profiles")
        .select("id, first_name, last_name")
        .in("id", ownerIds);

      if (ownersError) throw ownersError;

      // Fetch hotels
      let hotelQuery = supabase.from("hotels").select("*");

      if (activeTab === "pending") {
        hotelQuery = hotelQuery.eq("status", "pending");
      } else if (activeTab === "approved") {
        hotelQuery = hotelQuery.eq("status", "approved");
      }

      const { data: hotelsData, error: hotelsError } = await hotelQuery;

      if (hotelsError) {
        console.error("Hotels fetch error:", hotelsError);
        throw hotelsError;
      }

      console.log("Hotels fetched:", hotelsData?.length || 0);

      // Fetch cars
      let carQuery = supabase.from("cars").select("*");

      if (activeTab === "pending") {
        carQuery = carQuery.eq("status", "pending");
      } else if (activeTab === "approved") {
        carQuery = carQuery.eq("status", "approved");
      }

      const { data: carsData, error: carsError } = await carQuery;

      if (carsError) {
        console.error("Cars fetch error:", carsError);
        throw carsError;
      }

      console.log("Cars fetched:", carsData?.length || 0);

      // Create a map of owner data
      const ownersMap: Record<
        string,
        { first_name?: string; last_name?: string }
      > = {};
      ownersData?.forEach((owner) => {
        ownersMap[owner.id] = {
          first_name: owner.first_name || "",
          last_name: owner.last_name || "",
        };
      });

      // Transform the data to add owner_first_name and owner_last_name properties
      const transformedProperties =
        propertiesData?.map((property) => ({
          ...property,
          owner_first_name: ownersMap[property.owner_id]?.first_name || "",
          owner_last_name: ownersMap[property.owner_id]?.last_name || "",
        })) || [];

      const transformedHotels =
        hotelsData?.map((hotel) => ({
          ...hotel,
          owner_first_name: ownersMap[hotel.owner_id]?.first_name || "",
          owner_last_name: ownersMap[hotel.owner_id]?.last_name || "",
        })) || [];

      const transformedCars =
        carsData?.map((car) => ({
          ...car,
          owner_first_name: ownersMap[car.owner_id]?.first_name || "",
          owner_last_name: ownersMap[car.owner_id]?.last_name || "",
        })) || [];

      setProperties(transformedProperties);
      setHotels(transformedHotels);
      setCars(transformedCars);

      console.log(
        "Final state set - Properties:",
        transformedProperties.length,
        "Hotels:",
        transformedHotels.length,
        "Cars:",
        transformedCars.length
      );
    } catch (error: any) {
      console.error("Error fetching listings:", error);
      toast.error("Failed to fetch listings: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  const viewListingDetails = (listing: Listing) => {
    setSelectedListing(listing);
    setIsDetailDialogOpen(true);
  };

  // Check if a listing is a car listing
  const isCarListing = (listing: Listing): listing is CarListing => {
    return "make" in listing && "model" in listing;
  };

  // Check if a listing is a property listing
  const isPropertyListing = (listing: Listing): listing is PropertyListing => {
    return "beds" in listing && "baths" in listing;
  };

  // Check if a listing is a hotel listing
  const isHotelListing = (listing: Listing): listing is HotelListing => {
    return (
      "check_in_time" in listing &&
      "check_out_time" in listing &&
      "amenities" in listing
    );
  };

  const updateListingStatus = async (
    id: number,
    status: "pending" | "approved" | "rejected"
  ) => {
    try {
      console.log(`Updating listing ${id} to status: ${status}`);

      const updateData = {
        status,
        updated_at: new Date().toISOString(),
      };

      let tableName = "";
      if (isPropertyListing(selectedListing!)) {
        tableName = "properties";
        const { error } = await supabase
          .from("properties")
          .update(updateData)
          .eq("id", id);

        if (error) throw error;
      } else if (isHotelListing(selectedListing!)) {
        tableName = "hotels";
        const { error } = await supabase
          .from("hotels")
          .update(updateData)
          .eq("id", id);

        if (error) throw error;
      } else {
        tableName = "cars";
        const { error } = await supabase
          .from("cars")
          .update(updateData)
          .eq("id", id);

        if (error) throw error;
      }

      console.log(
        `Successfully updated ${tableName} listing ${id} to ${status}`
      );
      toast.success(`Listing status updated to ${status}`);

      // Refetch all listings to ensure data consistency
      console.log("Refetching listings after status update...");
      await fetchListings();
    } catch (error: any) {
      console.error("Error updating listing status:", error);
      toast.error("Failed to update listing status: " + error.message);
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Manage Listings</h1>

      <Tabs
        defaultValue="all"
        value={activeTab}
        onValueChange={setActiveTab}
        className="mb-6"
      >
        <TabsList className="mb-4">
          <TabsTrigger value="all">All Listings</TabsTrigger>
          <TabsTrigger value="pending">Pending Approval</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
        </TabsList>
      </Tabs>

      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
        </div>
      ) : (
        <>
          <h2 className="text-xl font-semibold mb-4">
            Properties ({properties.length})
          </h2>
          <div className="rounded-md border mb-8">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {properties.length > 0 ? (
                  properties.map((property) => (
                    <TableRow key={property.id}>
                      <TableCell className="font-medium">
                        {property.title}
                      </TableCell>
                      <TableCell>{property.location}</TableCell>
                      <TableCell>
                        {property.owner_first_name} {property.owner_last_name}
                      </TableCell>
                      <TableCell>${property.price}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            property.status === "approved"
                              ? "bg-green-100 text-green-800"
                              : property.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {property.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => viewListingDetails(property)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No properties found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          <h2 className="text-xl font-semibold mb-4">
            Hotels ({hotels.length})
          </h2>
          <div className="rounded-md border mb-8">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead>Check-in/out</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {hotels.length > 0 ? (
                  hotels.map((hotel) => (
                    <TableRow key={hotel.id}>
                      <TableCell className="font-medium">
                        {hotel.title}
                      </TableCell>
                      <TableCell>{hotel.location}</TableCell>
                      <TableCell>
                        {hotel.owner_first_name} {hotel.owner_last_name}
                      </TableCell>
                      <TableCell>
                        <div className="text-xs">
                          <div>In: {hotel.check_in_time}</div>
                          <div>Out: {hotel.check_out_time}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            hotel.status === "approved"
                              ? "bg-green-100 text-green-800"
                              : hotel.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {hotel.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => viewListingDetails(hotel)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No hotels found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          <h2 className="text-xl font-semibold mb-4">Cars ({cars.length})</h2>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Make/Model</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead>Price (Daily)</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {cars.length > 0 ? (
                  cars.map((car) => (
                    <TableRow key={car.id}>
                      <TableCell className="font-medium">{car.title}</TableCell>
                      <TableCell>
                        {car.make} {car.model}
                      </TableCell>
                      <TableCell>
                        {car.owner_first_name} {car.owner_last_name}
                      </TableCell>
                      <TableCell>${car.price_day}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            car.status === "approved"
                              ? "bg-green-100 text-green-800"
                              : car.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {car.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => viewListingDetails(car)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No cars found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </>
      )}

      {/* Listing Details Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Listing Details</DialogTitle>
          </DialogHeader>

          {selectedListing && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-lg">
                    {selectedListing.title}
                  </h3>
                  <p className="text-gray-600">{selectedListing.description}</p>
                </div>
                <div>
                  <div className="bg-gray-100 p-3 rounded-lg">
                    <h4 className="font-medium mb-2">Listing Info</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <span className="text-gray-600">Type:</span>
                      <span>
                        {isCarListing(selectedListing)
                          ? "Car"
                          : isHotelListing(selectedListing)
                          ? "Hotel"
                          : "Property"}
                      </span>

                      <span className="text-gray-600">Created:</span>
                      <span>
                        {new Date(
                          selectedListing.created_at
                        ).toLocaleDateString()}
                      </span>

                      <span className="text-gray-600">Status:</span>
                      <span
                        className={`
                        px-2 py-0.5 rounded-full text-xs inline-block
                        ${
                          selectedListing.status === "approved"
                            ? "bg-green-100 text-green-800"
                            : selectedListing.status === "rejected"
                            ? "bg-red-100 text-red-800"
                            : "bg-yellow-100 text-yellow-800"
                        }
                      `}
                      >
                        {selectedListing.status}
                      </span>

                      <span className="text-gray-600">ID:</span>
                      <span className="truncate">{selectedListing.id}</span>

                      {isCarListing(selectedListing) && (
                        <>
                          <span className="text-gray-600">Make/Model:</span>
                          <span>
                            {selectedListing.make} {selectedListing.model}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Price Information */}
              <div className="bg-gray-100 p-3 rounded-lg">
                <h4 className="font-medium mb-2">Price Information</h4>
                {isPropertyListing(selectedListing) ? (
                  <p className="text-lg font-bold">
                    ${selectedListing.price}/night
                  </p>
                ) : isHotelListing(selectedListing) ? (
                  <p className="text-lg font-bold">Contact for room rates</p>
                ) : (
                  <div className="grid grid-cols-3 gap-2">
                    <div>
                      <p className="text-gray-600">Daily</p>
                      <p className="font-bold">${selectedListing.price_day}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Weekly</p>
                      <p className="font-bold">${selectedListing.price_week}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Monthly</p>
                      <p className="font-bold">
                        ${selectedListing.price_month}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Property Specific Details */}
              {isPropertyListing(selectedListing) && (
                <div className="bg-gray-100 p-3 rounded-lg">
                  <h4 className="font-medium mb-2">Property Details</h4>
                  <div className="grid grid-cols-3 gap-2">
                    <div>
                      <p className="text-gray-600">Beds</p>
                      <p className="font-bold">{selectedListing.beds}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Baths</p>
                      <p className="font-bold">{selectedListing.baths}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Location</p>
                      <p className="font-bold">{selectedListing.location}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Hotel Specific Details */}
              {isHotelListing(selectedListing) && (
                <div className="bg-gray-100 p-3 rounded-lg">
                  <h4 className="font-medium mb-2">Hotel Details</h4>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <p className="text-gray-600">Amenities</p>
                      <p className="font-bold">
                        {selectedListing.amenities?.length || 0} amenities
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Location</p>
                      <p className="font-bold">{selectedListing.location}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Check-in Time</p>
                      <p className="font-bold">
                        {selectedListing.check_in_time}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Check-out Time</p>
                      <p className="font-bold">
                        {selectedListing.check_out_time}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Car Specific Details */}
              {isCarListing(selectedListing) && (
                <div className="bg-gray-100 p-3 rounded-lg">
                  <h4 className="font-medium mb-2">Car Details</h4>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <p className="text-gray-600">Year</p>
                      <p className="font-bold">{selectedListing.year}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Seats</p>
                      <p className="font-bold">{selectedListing.seats}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Transmission</p>
                      <p className="font-bold">
                        {selectedListing.transmission}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Fuel Type</p>
                      <p className="font-bold">{selectedListing.fuel_type}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Features/Amenities */}
              {isCarListing(selectedListing) && selectedListing.features && (
                <div className="bg-gray-100 p-3 rounded-lg">
                  <h4 className="font-medium mb-2">Features</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedListing.features.map((feature, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-200 rounded-full text-xs"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Hotel Amenities */}
              {isHotelListing(selectedListing) && selectedListing.amenities && (
                <div className="bg-gray-100 p-3 rounded-lg">
                  <h4 className="font-medium mb-2">Amenities</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedListing.amenities.map((amenity, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-200 rounded-full text-xs"
                      >
                        {amenity}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Images */}
              <div>
                <h4 className="font-medium mb-2">Images</h4>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                  {selectedListing.images &&
                  selectedListing.images.length > 0 ? (
                    selectedListing.images.map((image, index) => (
                      <div
                        key={index}
                        className="aspect-square bg-gray-200 rounded-md overflow-hidden"
                      >
                        <img
                          src={image}
                          alt={`${selectedListing.title} image ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ))
                  ) : (
                    <p className="col-span-3 text-gray-500 italic">
                      No images available
                    </p>
                  )}
                </div>
              </div>

              <DialogFooter className="flex justify-between gap-2">
                {selectedListing.status === "pending" && (
                  <>
                    <Button
                      variant="destructive"
                      onClick={() => {
                        updateListingStatus(selectedListing.id, "rejected");
                        setIsDetailDialogOpen(false);
                      }}
                    >
                      Reject Listing
                    </Button>
                    <Button
                      variant="default"
                      onClick={() => {
                        updateListingStatus(selectedListing.id, "approved");
                        setIsDetailDialogOpen(false);
                      }}
                    >
                      Approve Listing
                    </Button>
                  </>
                )}
                {selectedListing.status === "approved" && (
                  <Button
                    variant="destructive"
                    onClick={() => {
                      updateListingStatus(selectedListing.id, "rejected");
                      setIsDetailDialogOpen(false);
                    }}
                  >
                    Suspend Listing
                  </Button>
                )}
                {selectedListing.status === "rejected" && (
                  <Button
                    variant="default"
                    onClick={() => {
                      updateListingStatus(selectedListing.id, "approved");
                      setIsDetailDialogOpen(false);
                    }}
                  >
                    Reinstate Listing
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={() => setIsDetailDialogOpen(false)}
                >
                  Close
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminListings;
